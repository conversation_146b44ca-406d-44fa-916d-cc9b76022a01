<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Moonlight</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      height: 100vh;
      background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
      overflow: hidden;
      font-family: 'Poppins', sans-serif;
      color: white;
      text-align: center;
    }

    /* Stars */
    .stars {
      width: 2px;
      height: 2px;
      background: white;
      position: absolute;
      top: 0;
      animation: twinkle 2s infinite ease-in-out;
    }

    @keyframes twinkle {
      0%, 100% { opacity: 0.2; }
      50% { opacity: 1; }
    }

    /* Moon */
    .moon {
      width: 280px;
      height: 280px;
      background: radial-gradient(circle at 35% 25%, #ffffff 0%, #f8f8f8 20%, #e8e8e8 40%, #d0d0d0 70%, #b8b8b8 100%);
      border-radius: 50%;
      position: relative;
      margin: 80px auto 30px;
      animation: moonFloat 6s ease-in-out infinite, moonGlow 4s ease-in-out infinite alternate;
      box-shadow:
        0 0 100px 30px rgba(255, 255, 255, 0.4),
        0 0 200px 60px rgba(255, 255, 255, 0.2),
        inset -20px -20px 40px rgba(0, 0, 0, 0.1);
      filter: drop-shadow(0 0 50px rgba(255, 255, 255, 0.6));
    }

    .moon::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: radial-gradient(circle at 40% 30%, rgba(255, 255, 255, 0.8) 0%, transparent 50%);
      top: 0;
      left: 0;
    }

    .moon::after {
      content: '';
      position: absolute;
      width: 25px;
      height: 25px;
      background: rgba(0, 0, 0, 0.08);
      border-radius: 50%;
      top: 60px;
      left: 80px;
      box-shadow:
        40px 30px 0 -5px rgba(0, 0, 0, 0.06),
        -20px 80px 0 -8px rgba(0, 0, 0, 0.05),
        60px 120px 0 -10px rgba(0, 0, 0, 0.04);
    }

    @keyframes moonFloat {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      25% { transform: translateY(-15px) rotate(1deg); }
      50% { transform: translateY(-10px) rotate(0deg); }
      75% { transform: translateY(-20px) rotate(-1deg); }
    }

    @keyframes moonGlow {
      from {
        box-shadow:
          0 0 80px 25px rgba(255, 255, 255, 0.3),
          0 0 160px 50px rgba(255, 255, 255, 0.15),
          inset -20px -20px 40px rgba(0, 0, 0, 0.1);
        filter: drop-shadow(0 0 40px rgba(255, 255, 255, 0.5));
      }
      to {
        box-shadow:
          0 0 120px 40px rgba(255, 255, 255, 0.5),
          0 0 240px 80px rgba(255, 255, 255, 0.25),
          inset -20px -20px 40px rgba(0, 0, 0, 0.1);
        filter: drop-shadow(0 0 70px rgba(255, 255, 255, 0.8));
      }
    }

    /* Text */
    h1 {
      font-size: 2.5rem;
      opacity: 0;
      animation: fadeIn 4s forwards;
    }

    p {
      font-size: 1.2rem;
      opacity: 0;
      animation: fadeIn 6s forwards;
    }

    @keyframes fadeIn {
      to { opacity: 1; }
    }

    /* Floating Particles */
    .particle {
      position: absolute;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      animation: float 8s infinite ease-in-out;
      pointer-events: none;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.3; }
      25% { transform: translateY(-30px) translateX(10px) scale(1.2); opacity: 0.8; }
      50% { transform: translateY(-60px) translateX(-5px) scale(0.8); opacity: 0.5; }
      75% { transform: translateY(-40px) translateX(15px) scale(1.1); opacity: 0.7; }
    }

    /* Shooting Stars */
    .shooting-star {
      position: absolute;
      width: 2px;
      height: 2px;
      background: linear-gradient(45deg, #fff, transparent);
      border-radius: 50%;
      animation: shoot 3s linear infinite;
      opacity: 0;
    }

    @keyframes shoot {
      0% {
        opacity: 0;
        transform: translateX(0px) translateY(0px) scale(0);
      }
      10% {
        opacity: 1;
        transform: translateX(0px) translateY(0px) scale(1);
      }
      90% {
        opacity: 1;
        transform: translateX(300px) translateY(150px) scale(0.5);
        box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.8);
      }
      100% {
        opacity: 0;
        transform: translateX(400px) translateY(200px) scale(0);
      }
    }

    /* Soft Clouds */
    .cloud {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50px;
      animation: drift 20s infinite linear;
      opacity: 0.3;
    }

    .cloud::before,
    .cloud::after {
      content: '';
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50px;
    }

    @keyframes drift {
      0% { transform: translateX(-100px); }
      100% { transform: translateX(calc(100vw + 100px)); }
    }

    /* Moon Rays */
    .moon-ray {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 2px;
      height: 100px;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), transparent);
      transform-origin: bottom center;
      animation: rotate 30s linear infinite;
      opacity: 0.4;
    }

    @keyframes rotate {
      0% { transform: translate(-50%, -100%) rotate(0deg); }
      100% { transform: translate(-50%, -100%) rotate(360deg); }
    }

    /* Button */
    button {
      margin-top: 20px;
      padding: 12px 25px;
      font-size: 1rem;
      font-weight: bold;
      background: #fff;
      color: #1b2735;
      border: none;
      border-radius: 25px;
      cursor: pointer;
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
      transition: 0.3s ease;
    }
    button:hover {
      background: #ffccf9;
      color: #000;
      box-shadow: 0 0 20px rgba(255, 200, 250, 1);
    }

    /* Surprise Message */
    #surprise {
      margin-top: 30px;
      font-size: 1.5rem;
      opacity: 0;
      transform: scale(0.8);
      transition: all 0.8s ease;
    }
    #surprise.show {
      opacity: 1;
      transform: scale(1);
      color: #ffd700;
      text-shadow: 0 0 15px #fff, 0 0 30px #ffd700;
    }
  </style>
</head>
<body>
  <!-- Moon Rays -->
  <div class="moon-ray" style="transform: translate(-50%, -100%) rotate(0deg);"></div>
  <div class="moon-ray" style="transform: translate(-50%, -100%) rotate(45deg);"></div>
  <div class="moon-ray" style="transform: translate(-50%, -100%) rotate(90deg);"></div>
  <div class="moon-ray" style="transform: translate(-50%, -100%) rotate(135deg);"></div>
  <div class="moon-ray" style="transform: translate(-50%, -100%) rotate(180deg);"></div>
  <div class="moon-ray" style="transform: translate(-50%, -100%) rotate(225deg);"></div>
  <div class="moon-ray" style="transform: translate(-50%, -100%) rotate(270deg);"></div>
  <div class="moon-ray" style="transform: translate(-50%, -100%) rotate(315deg);"></div>

  <div class="moon"></div>
  <h1>Hello Moonlight 🌙</h1>
  <p>Tumhari vibes bilkul full moon jaisi hain – calm but powerful ✨</p>

  <button onclick="showSurprise()">Click for Surprise 💌</button>
  <div id="surprise">Every star in the sky reminds me of you, my Moonlight ✨🌙</div>

  <script>
    // Stars random generate
    const numStars = 150;
    for (let i = 0; i < numStars; i++) {
      let star = document.createElement("div");
      star.className = "stars";
      star.style.top = Math.random() * window.innerHeight + "px";
      star.style.left = Math.random() * window.innerWidth + "px";
      star.style.animationDelay = Math.random() * 2 + "s";
      document.body.appendChild(star);
    }

    // Floating Particles
    const numParticles = 20;
    for (let i = 0; i < numParticles; i++) {
      let particle = document.createElement("div");
      particle.className = "particle";
      particle.style.width = (Math.random() * 4 + 2) + "px";
      particle.style.height = particle.style.width;
      particle.style.top = Math.random() * window.innerHeight + "px";
      particle.style.left = Math.random() * window.innerWidth + "px";
      particle.style.animationDelay = Math.random() * 8 + "s";
      particle.style.animationDuration = (Math.random() * 6 + 6) + "s";
      document.body.appendChild(particle);
    }

    // Shooting Stars
    function createShootingStar() {
      let shootingStar = document.createElement("div");
      shootingStar.className = "shooting-star";
      shootingStar.style.top = Math.random() * (window.innerHeight / 2) + "px";
      shootingStar.style.left = Math.random() * (window.innerWidth / 2) + "px";
      shootingStar.style.animationDelay = Math.random() * 2 + "s";
      document.body.appendChild(shootingStar);

      setTimeout(() => {
        shootingStar.remove();
      }, 3000);
    }

    // Create shooting stars periodically
    setInterval(createShootingStar, 4000);

    // Soft Clouds
    function createCloud() {
      let cloud = document.createElement("div");
      cloud.className = "cloud";
      let size = Math.random() * 80 + 40;
      cloud.style.width = size + "px";
      cloud.style.height = size * 0.6 + "px";
      cloud.style.top = Math.random() * (window.innerHeight * 0.7) + "px";
      cloud.style.left = "-100px";
      cloud.style.animationDuration = (Math.random() * 10 + 15) + "s";

      // Add cloud parts
      cloud.innerHTML = `
        <div style="position: absolute; width: ${size * 0.7}px; height: ${size * 0.4}px;
                    background: rgba(255, 255, 255, 0.08); border-radius: 50px;
                    top: ${size * 0.1}px; left: ${size * 0.2}px;"></div>
        <div style="position: absolute; width: ${size * 0.5}px; height: ${size * 0.3}px;
                    background: rgba(255, 255, 255, 0.06); border-radius: 50px;
                    top: ${size * 0.2}px; left: ${size * 0.6}px;"></div>
      `;

      document.body.appendChild(cloud);

      setTimeout(() => {
        cloud.remove();
      }, 25000);
    }

    // Create clouds periodically
    setInterval(createCloud, 8000);
    createCloud(); // Create first cloud immediately

    // Surprise message function
    function showSurprise() {
      document.getElementById("surprise").classList.add("show");
    }
  </script>
</body>
</html>
