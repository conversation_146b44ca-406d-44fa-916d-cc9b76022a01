<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Moonlight</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      height: 100vh;
      background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
      overflow: hidden;
      font-family: 'Poppins', sans-serif;
      color: white;
      text-align: center;
    }

    /* Stars */
    .stars {
      width: 2px;
      height: 2px;
      background: white;
      position: absolute;
      top: 0;
      animation: twinkle 2s infinite ease-in-out;
    }

    @keyframes twinkle {
      0%, 100% { opacity: 0.2; }
      50% { opacity: 1; }
    }

    /* Moon */
    .moon {
      width: 280px;
      height: 280px;
      background: radial-gradient(circle at 35% 25%, #ffffff 0%, #f8f8f8 20%, #e8e8e8 40%, #d0d0d0 70%, #b8b8b8 100%);
      border-radius: 50%;
      position: relative;
      margin: 80px auto 30px;
      animation: moonFloat 6s ease-in-out infinite, moonGlow 4s ease-in-out infinite alternate;
      box-shadow:
        0 0 100px 30px rgba(255, 255, 255, 0.4),
        0 0 200px 60px rgba(255, 255, 255, 0.2),
        inset -20px -20px 40px rgba(0, 0, 0, 0.1);
      filter: drop-shadow(0 0 50px rgba(255, 255, 255, 0.6));
    }

    .moon::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: radial-gradient(circle at 40% 30%, rgba(255, 255, 255, 0.8) 0%, transparent 50%);
      top: 0;
      left: 0;
    }

    .moon::after {
      content: '';
      position: absolute;
      width: 25px;
      height: 25px;
      background: rgba(0, 0, 0, 0.08);
      border-radius: 50%;
      top: 60px;
      left: 80px;
      box-shadow:
        40px 30px 0 -5px rgba(0, 0, 0, 0.06),
        -20px 80px 0 -8px rgba(0, 0, 0, 0.05),
        60px 120px 0 -10px rgba(0, 0, 0, 0.04);
    }

    @keyframes moonFloat {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      25% { transform: translateY(-15px) rotate(1deg); }
      50% { transform: translateY(-10px) rotate(0deg); }
      75% { transform: translateY(-20px) rotate(-1deg); }
    }

    @keyframes moonGlow {
      from {
        box-shadow:
          0 0 80px 25px rgba(255, 255, 255, 0.3),
          0 0 160px 50px rgba(255, 255, 255, 0.15),
          inset -20px -20px 40px rgba(0, 0, 0, 0.1);
        filter: drop-shadow(0 0 40px rgba(255, 255, 255, 0.5));
      }
      to {
        box-shadow:
          0 0 120px 40px rgba(255, 255, 255, 0.5),
          0 0 240px 80px rgba(255, 255, 255, 0.25),
          inset -20px -20px 40px rgba(0, 0, 0, 0.1);
        filter: drop-shadow(0 0 70px rgba(255, 255, 255, 0.8));
      }
    }

    /* Text */
    h1 {
      font-size: 2.5rem;
      opacity: 0;
      animation: fadeIn 4s forwards;
    }

    p {
      font-size: 1.2rem;
      opacity: 0;
      animation: fadeIn 6s forwards;
    }

    @keyframes fadeIn {
      to { opacity: 1; }
    }

    /* Button */
    button {
      margin-top: 20px;
      padding: 12px 25px;
      font-size: 1rem;
      font-weight: bold;
      background: #fff;
      color: #1b2735;
      border: none;
      border-radius: 25px;
      cursor: pointer;
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
      transition: 0.3s ease;
    }
    button:hover {
      background: #ffccf9;
      color: #000;
      box-shadow: 0 0 20px rgba(255, 200, 250, 1);
    }

    /* Surprise Message */
    #surprise {
      margin-top: 30px;
      font-size: 1.5rem;
      opacity: 0;
      transform: scale(0.8);
      transition: all 0.8s ease;
    }
    #surprise.show {
      opacity: 1;
      transform: scale(1);
      color: #ffd700;
      text-shadow: 0 0 15px #fff, 0 0 30px #ffd700;
    }
  </style>
</head>
<body>
  <div class="moon"></div>
  <h1>Hello Moonlight 🌙</h1>
  <p>Tumhari vibes bilkul full moon jaisi hain – calm but powerful ✨</p>

  <button onclick="showSurprise()">Click for Surprise 💌</button>
  <div id="surprise">Every star in the sky reminds me of you, my Moonlight ✨🌙</div>

  <script>
    // Stars random generate
    const numStars = 100;
    for (let i = 0; i < numStars; i++) {
      let star = document.createElement("div");
      star.className = "stars";
      star.style.top = Math.random() * window.innerHeight + "px";
      star.style.left = Math.random() * window.innerWidth + "px";
      document.body.appendChild(star);
    }

    // Surprise message function
    function showSurprise() {
      document.getElementById("surprise").classList.add("show");
    }
  </script>
</body>
</html>
