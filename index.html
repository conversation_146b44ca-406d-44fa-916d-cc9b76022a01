<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Moonlight</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      height: 100vh;
      background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
      overflow: hidden;
      font-family: 'Poppins', sans-serif;
      color: white;
      text-align: center;
    }

    /* Stars */
    .stars {
      width: 2px;
      height: 2px;
      background: white;
      position: absolute;
      top: 0;
      animation: twinkle 2s infinite ease-in-out;
    }

    @keyframes twinkle {
      0%, 100% { opacity: 0.2; }
      50% { opacity: 1; }
    }

    /* Moon */
    .moon {
      width: 150px;
      height: 150px;
      background: radial-gradient(circle at 30% 30%, #fff, #d6d6d6);
      border-radius: 50%;
      box-shadow: 0 0 60px 20px rgba(255, 255, 255, 0.5);
      margin: 100px auto 20px;
      animation: glow 3s infinite alternate;
    }

    @keyframes glow {
      from { box-shadow: 0 0 40px 10px rgba(255, 255, 255, 0.3); }
      to { box-shadow: 0 0 80px 30px rgba(255, 255, 255, 0.7); }
    }

    /* Text */
    h1 {
      font-size: 2.5rem;
      opacity: 0;
      animation: fadeIn 4s forwards;
    }

    p {
      font-size: 1.2rem;
      opacity: 0;
      animation: fadeIn 6s forwards;
    }

    @keyframes fadeIn {
      to { opacity: 1; }
    }

    /* Button */
    button {
      margin-top: 20px;
      padding: 12px 25px;
      font-size: 1rem;
      font-weight: bold;
      background: #fff;
      color: #1b2735;
      border: none;
      border-radius: 25px;
      cursor: pointer;
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
      transition: 0.3s ease;
    }
    button:hover {
      background: #ffccf9;
      color: #000;
      box-shadow: 0 0 20px rgba(255, 200, 250, 1);
    }

    /* Surprise Message */
    #surprise {
      margin-top: 30px;
      font-size: 1.5rem;
      opacity: 0;
      transform: scale(0.8);
      transition: all 0.8s ease;
    }
    #surprise.show {
      opacity: 1;
      transform: scale(1);
      color: #ffd700;
      text-shadow: 0 0 15px #fff, 0 0 30px #ffd700;
    }
  </style>
</head>
<body>
  <div class="moon"></div>
  <h1>Hello Moonlight 🌙</h1>
  <p>Tumhari vibes bilkul full moon jaisi hain – calm but powerful ✨</p>

  <button onclick="showSurprise()">Click for Surprise 💌</button>
  <div id="surprise">Every star in the sky reminds me of you, my Moonlight ✨🌙</div>

  <script>
    // Stars random generate
    const numStars = 100;
    for (let i = 0; i < numStars; i++) {
      let star = document.createElement("div");
      star.className = "stars";
      star.style.top = Math.random() * window.innerHeight + "px";
      star.style.left = Math.random() * window.innerWidth + "px";
      document.body.appendChild(star);
    }

    // Surprise message function
    function showSurprise() {
      document.getElementById("surprise").classList.add("show");
    }
  </script>
</body>
</html>
